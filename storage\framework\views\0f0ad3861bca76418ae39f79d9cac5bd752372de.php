<?php $__env->startSection('content'); ?>
<div class="content container-fluid">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707 = $component; } ?>
<?php $component = App\View\Components\PageHeader::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\PageHeader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('icon', null, []); ?> credit-card <?php $__env->endSlot(); ?>
         <?php $__env->slot('title', null, []); ?> Payment Details <?php $__env->endSlot(); ?>
         <?php $__env->slot('subtitle', null, []); ?> View payment information and related records <?php $__env->endSlot(); ?>
         <?php $__env->slot('breadcrumbs', null, []); ?> 
            <li class="breadcrumb-item"><a href="/">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('payments.index')); ?>">Payments</a></li>
            <li class="breadcrumb-item active" aria-current="page">Payment #<?php echo e($payment->id); ?></li>
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('controls', null, []); ?> 
            <div class="d-flex gap-2">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $payment)): ?>
                <a href="<?php echo e(route('payments.edit', $payment)); ?>" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-pencil me-1"></i>Edit Payment
                </a>
                <?php endif; ?>
                <a href="<?php echo e(route('payments.index')); ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i>Back to Payments
                </a>
            </div>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707)): ?>
<?php $component = $__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707; ?>
<?php unset($__componentOriginal013227a727690dac4a34b0837e90e96dcfa3f707); ?>
<?php endif; ?>

    <div class="row">
        <!-- Payment Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-credit-card me-2"></i>Payment Information
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label text-muted">Payment ID</label>
                                <div class="fw-bold">#<?php echo e($payment->id); ?></div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Amount</label>
                                <div class="fw-bold h4 text-success"><?php echo e(_money($payment->amount)); ?></div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Payment Type</label>
                                <div>
                                    <span class="badge bg-primary"><?php echo e($payment->payment_type); ?></span>
                                    <?php if($payment->paymentable): ?>
                                        <span class="text-muted ms-2">#<?php echo e($payment->paymentable_id); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Reference Number</label>
                                <div class="fw-semibold">
                                    <?php echo e($payment->reference_no ?? '-'); ?>

                                </div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Currency</label>
                                <div>
                                    <?php if($payment->currency): ?>
                                        <span class="badge bg-light text-dark"><?php echo e($payment->currency->code); ?></span>
                                        <span class="text-muted ms-2"><?php echo e($payment->currency->name); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">Default Currency</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label text-muted">Date & Time</label>
                                <div class="fw-semibold"><?php echo e($payment->created_at->format('M d, Y h:i A')); ?></div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Balance After Payment</label>
                                <div class="fw-semibold"><?php echo e(_money($payment->balance)); ?></div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-muted">Created By</label>
                                <div class="d-flex align-items-center">
                                    <?php if($payment->createdBy): ?>
                                        <div class="avatar avatar-xs avatar-circle me-2">
                                            <span class="avatar-initials bg-primary text-white">
                                                <?php echo e(substr($payment->createdBy->name, 0, 1)); ?>

                                            </span>
                                        </div>
                                        <span class="fw-semibold"><?php echo e($payment->createdBy->name); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if($payment->updatedBy && $payment->updated_at != $payment->created_at): ?>
                            <div class="mb-4">
                                <label class="form-label text-muted">Last Updated By</label>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-xs avatar-circle me-2">
                                        <span class="avatar-initials bg-secondary text-white">
                                            <?php echo e(substr($payment->updatedBy->name, 0, 1)); ?>

                                        </span>
                                    </div>
                                    <div>
                                        <div class="fw-semibold"><?php echo e($payment->updatedBy->name); ?></div>
                                        <small class="text-muted"><?php echo e($payment->updated_at->format('M d, Y h:i A')); ?></small>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if($payment->description): ?>
                    <div class="mb-4">
                        <label class="form-label text-muted">Description</label>
                        <div class="fw-semibold"><?php echo e($payment->description); ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if($payment->comment): ?>
                    <div class="mb-4">
                        <label class="form-label text-muted">Internal Comments</label>
                        <div class="fw-semibold"><?php echo e($payment->comment); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Related Record Information -->
            <?php if($payment->paymentable): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-link-45deg me-2"></i>Related <?php echo e($payment->payment_type); ?>

                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo e($payment->payment_type); ?> ID</label>
                                <div class="fw-bold">#<?php echo e($payment->paymentable->id); ?></div>
                            </div>

                            <?php if($payment->paymentable->customer): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">Customer</label>
                                <div class="fw-semibold"><?php echo e($payment->paymentable->customer->name); ?></div>
                            </div>
                            <?php endif; ?>

                            <div class="mb-3">
                                <label class="form-label text-muted">Total Amount</label>
                                <div class="fw-semibold"><?php echo e(_money($payment->paymentable->amount_total)); ?></div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Amount Paid</label>
                                <div class="fw-semibold text-success"><?php echo e(_money($payment->paymentable->amount_paid)); ?></div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Outstanding Balance</label>
                                <div class="fw-semibold <?php echo e($payment->paymentable->balance > 0 ? 'text-danger' : 'text-success'); ?>">
                                    <?php echo e(_money($payment->paymentable->balance)); ?>

                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    <?php if($payment->paymentable->balance <= 0): ?>
                                        <span class="badge bg-success">Fully Paid</span>
                                    <?php elseif($payment->paymentable->amount_paid > 0): ?>
                                        <span class="badge bg-warning">Partially Paid</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Unpaid</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <?php if($payment->paymentable_type === 'App\Models\Invoice'): ?>
                            <a href="<?php echo e(route('invoices.show', $payment->paymentable)); ?>" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye me-1"></i>View Invoice
                            </a>
                        <?php elseif($payment->paymentable_type === 'App\Models\Order'): ?>
                            <a href="<?php echo e(route('orders.show', $payment->paymentable)); ?>" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye me-1"></i>View Order
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Actions Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-gear me-2"></i>Actions
                    </h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $payment)): ?>
                        <a href="<?php echo e(route('payments.edit', $payment)); ?>" class="btn btn-outline-primary">
                            <i class="bi bi-pencil me-2"></i>Edit Payment
                        </a>
                        <?php endif; ?>

                        <?php if($payment->paymentable): ?>
                            <?php if($payment->paymentable_type === 'App\Models\Invoice'): ?>
                                <a href="<?php echo e(route('invoices.show', $payment->paymentable)); ?>" class="btn btn-outline-info">
                                    <i class="bi bi-receipt me-2"></i>View Invoice
                                </a>
                            <?php elseif($payment->paymentable_type === 'App\Models\Order'): ?>
                                <a href="<?php echo e(route('orders.show', $payment->paymentable)); ?>" class="btn btn-outline-info">
                                    <i class="bi bi-cart me-2"></i>View Order
                                </a>
                            <?php endif; ?>
                        <?php endif; ?>

                        <a href="<?php echo e(route('payments.create')); ?>" class="btn btn-outline-success">
                            <i class="bi bi-plus-circle me-2"></i>Record New Payment
                        </a>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $payment)): ?>
                        <hr>
                        <form action="<?php echo e(route('payments.destroy', $payment)); ?>" method="POST" 
                              onsubmit="return confirm('Are you sure you want to delete this payment? This action cannot be undone and will affect the related invoice/order balance.')">
                            <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="bi bi-trash me-2"></i>Delete Payment
                            </button>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Payment Summary -->
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-header-title">
                        <i class="bi bi-info-circle me-2"></i>Payment Summary
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <div class="border rounded p-3">
                                <div class="h4 text-success mb-1"><?php echo e(_money($payment->amount)); ?></div>
                                <div class="text-muted small">Payment Amount</div>
                            </div>
                        </div>
                        <?php if($payment->paymentable): ?>
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <div class="h6 mb-1"><?php echo e(_money($payment->paymentable->amount_paid)); ?></div>
                                <div class="text-muted small">Total Paid</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <div class="h6 mb-1 <?php echo e($payment->paymentable->balance > 0 ? 'text-danger' : 'text-success'); ?>">
                                    <?php echo e(_money($payment->paymentable->balance)); ?>

                                </div>
                                <div class="text-muted small">Remaining</div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Acounting\github\accounting\resources\views/app/payments/show.blade.php ENDPATH**/ ?>