<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Recording Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Payment Recording Test</h2>
        <p>This page tests the payment recording functionality by simulating the AJAX calls.</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Customer API</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testCustomerAPI()">Test Customer API</button>
                        <div id="customerResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Supplier API</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testSupplierAPI()">Test Supplier API</button>
                        <div id="supplierResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Payment Recording</h5>
                    </div>
                    <div class="card-body">
                        <form id="testPaymentForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Payment Type</label>
                                    <select class="form-select" name="payment_type" required>
                                        <option value="">Select Type</option>
                                        <option value="receivable">Receivable (Customer Payment)</option>
                                        <option value="payable">Payable (Supplier Payment)</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Customer/Supplier ID</label>
                                    <input type="number" class="form-control" name="entity_id" placeholder="Enter ID" required>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label class="form-label">Invoice/Order ID</label>
                                    <input type="number" class="form-control" name="invoice_id" placeholder="Enter Invoice/Order ID" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Amount</label>
                                    <input type="number" class="form-control" name="amount" step="0.01" placeholder="Enter Amount" required>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label class="form-label">Reference No</label>
                                    <input type="text" class="form-control" name="reference_no" placeholder="Optional reference">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Payment Method</label>
                                    <input type="text" class="form-control" name="payment_method" placeholder="e.g., Cash, Bank Transfer">
                                </div>
                            </div>
                            <div class="mt-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" name="description" rows="2" placeholder="Payment description"></textarea>
                            </div>
                            <div class="mt-3">
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-credit-card me-1"></i>Test Record Payment
                                </button>
                            </div>
                        </form>
                        <div id="paymentResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test Customer API
        function testCustomerAPI() {
            const resultDiv = document.getElementById('customerResult');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm"></div> Loading...';
            
            fetch('http://localhost:8000/customers/api')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (Array.isArray(data)) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <strong>Success!</strong> Found ${data.length} customers.
                                <details class="mt-2">
                                    <summary>View Data</summary>
                                    <pre class="mt-2">${JSON.stringify(data, null, 2)}</pre>
                                </details>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="alert alert-warning">
                                <strong>Unexpected Response:</strong>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>Error:</strong> ${error.message}
                        </div>
                    `;
                });
        }
        
        // Test Supplier API
        function testSupplierAPI() {
            const resultDiv = document.getElementById('supplierResult');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm"></div> Loading...';
            
            fetch('http://localhost:8000/suppliers/api')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (Array.isArray(data)) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <strong>Success!</strong> Found ${data.length} suppliers.
                                <details class="mt-2">
                                    <summary>View Data</summary>
                                    <pre class="mt-2">${JSON.stringify(data, null, 2)}</pre>
                                </details>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="alert alert-warning">
                                <strong>Unexpected Response:</strong>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>Error:</strong> ${error.message}
                        </div>
                    `;
                });
        }
        
        // Test Payment Recording
        document.getElementById('testPaymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('paymentResult');
            const formData = new FormData(this);
            
            // Add customer_id or supplier_id based on payment type
            const paymentType = formData.get('payment_type');
            const entityId = formData.get('entity_id');
            
            if (paymentType === 'receivable') {
                formData.append('customer_id', entityId);
            } else if (paymentType === 'payable') {
                formData.append('supplier_id', entityId);
            }
            
            // Add CSRF token (you'll need to get this from the Laravel app)
            // For testing, we'll simulate without CSRF
            
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm"></div> Recording payment...';
            
            fetch('http://localhost:8000/payments/ajax', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                return response.json().then(data => ({
                    status: response.status,
                    ok: response.ok,
                    data: data
                }));
            })
            .then(({status, ok, data}) => {
                if (ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <strong>Payment Recorded Successfully!</strong><br>
                            Payment ID: ${data.payment_id}<br>
                            Message: ${data.message}
                        </div>
                    `;
                    // Reset form
                    document.getElementById('testPaymentForm').reset();
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>Payment Failed:</strong><br>
                            Status: ${status}<br>
                            Message: ${data.message || 'Unknown error'}
                            <details class="mt-2">
                                <summary>Full Response</summary>
                                <pre class="mt-2">${JSON.stringify(data, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Network Error:</strong> ${error.message}
                    </div>
                `;
            });
        });
    </script>
</body>
</html>
